# Russian (Russia) translations for PROJECT.
# Copyright (C) 2013 ORGANIZATION
# This file is distributed under the same license as the PROJECT project.
# <AUTHOR> <EMAIL>, 2013.
#
msgid ""
msgstr ""
"Project-Id-Version: PROJECT VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-08 19:22+0200\n"
"PO-Revision-Date: 2014-03-24 20:32+0300\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: ru_RU <<EMAIL>>\n"
"Language: ru\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && "
"n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"
"Generated-By: Babel 0.9.6\n"
"X-Generator: Poedit 1.5.4\n"

#: src/humanize/number.py:84
msgctxt "0 (male)"
msgid "th"
msgstr "ой"

#: src/humanize/number.py:85
msgctxt "1 (male)"
msgid "st"
msgstr "ый"

#: src/humanize/number.py:86
msgctxt "2 (male)"
msgid "nd"
msgstr "ой"

#: src/humanize/number.py:87
msgctxt "3 (male)"
msgid "rd"
msgstr "ий"

#: src/humanize/number.py:88
msgctxt "4 (male)"
msgid "th"
msgstr "ый"

#: src/humanize/number.py:89
msgctxt "5 (male)"
msgid "th"
msgstr "ый"

#: src/humanize/number.py:90
msgctxt "6 (male)"
msgid "th"
msgstr "ой"

#: src/humanize/number.py:91
msgctxt "7 (male)"
msgid "th"
msgstr "ой"

#: src/humanize/number.py:92
msgctxt "8 (male)"
msgid "th"
msgstr "ой"

#: src/humanize/number.py:93
msgctxt "9 (male)"
msgid "th"
msgstr "ый"

#: src/humanize/number.py:97
msgctxt "0 (female)"
msgid "th"
msgstr "ой"

#: src/humanize/number.py:98
msgctxt "1 (female)"
msgid "st"
msgstr "ый"

#: src/humanize/number.py:99
msgctxt "2 (female)"
msgid "nd"
msgstr "ой"

#: src/humanize/number.py:100
msgctxt "3 (female)"
msgid "rd"
msgstr "ий"

#: src/humanize/number.py:101
msgctxt "4 (female)"
msgid "th"
msgstr "ый"

#: src/humanize/number.py:102
msgctxt "5 (female)"
msgid "th"
msgstr "ый"

#: src/humanize/number.py:103
msgctxt "6 (female)"
msgid "th"
msgstr "ой"

#: src/humanize/number.py:104
msgctxt "7 (female)"
msgid "th"
msgstr "ой"

#: src/humanize/number.py:105
msgctxt "8 (female)"
msgid "th"
msgstr "ой"

#: src/humanize/number.py:106
msgctxt "9 (female)"
msgid "th"
msgstr "ый"

#: src/humanize/number.py:178
msgid "thousand"
msgid_plural "thousand"
msgstr[0] "тысяча"
msgstr[1] "тысячи"
msgstr[2] "тысяч"

#: src/humanize/number.py:179
msgid "million"
msgid_plural "million"
msgstr[0] "миллион"
msgstr[1] "миллиона"
msgstr[2] "миллионов"

#: src/humanize/number.py:180
msgid "billion"
msgid_plural "billion"
msgstr[0] "миллиард"
msgstr[1] "миллиарда"
msgstr[2] "миллиардов"

#: src/humanize/number.py:181
msgid "trillion"
msgid_plural "trillion"
msgstr[0] "триллион"
msgstr[1] "триллиона"
msgstr[2] "триллионов"

#: src/humanize/number.py:182
msgid "quadrillion"
msgid_plural "quadrillion"
msgstr[0] "квадриллион"
msgstr[1] "квадриллиона"
msgstr[2] "квадриллионов"

#: src/humanize/number.py:183
msgid "quintillion"
msgid_plural "quintillion"
msgstr[0] "квинтиллион"
msgstr[1] "квинтиллиона"
msgstr[2] "квинтиллионов"

#: src/humanize/number.py:184
msgid "sextillion"
msgid_plural "sextillion"
msgstr[0] "сикстиллион"
msgstr[1] "сикстиллиона"
msgstr[2] "сикстиллионов"

#: src/humanize/number.py:185
msgid "septillion"
msgid_plural "septillion"
msgstr[0] "септиллион"
msgstr[1] "септиллиона"
msgstr[2] "септиллионов"

#: src/humanize/number.py:186
msgid "octillion"
msgid_plural "octillion"
msgstr[0] "октиллион"
msgstr[1] "октиллиона"
msgstr[2] "октиллионов"

#: src/humanize/number.py:187
msgid "nonillion"
msgid_plural "nonillion"
msgstr[0] "нониллион"
msgstr[1] "нониллиона"
msgstr[2] "нониллионов"

#: src/humanize/number.py:188
msgid "decillion"
msgid_plural "decillion"
msgstr[0] "децилион"
msgstr[1] "децилиона"
msgstr[2] "децилионов"

#: src/humanize/number.py:189
msgid "googol"
msgid_plural "googol"
msgstr[0] "гогол"
msgstr[1] "гогола"
msgstr[2] "гоголов"

#: src/humanize/number.py:301
msgid "zero"
msgstr "ноль"

#: src/humanize/number.py:302
msgid "one"
msgstr "один"

#: src/humanize/number.py:303
msgid "two"
msgstr "два"

#: src/humanize/number.py:304
msgid "three"
msgstr "три"

#: src/humanize/number.py:305
msgid "four"
msgstr "четыре"

#: src/humanize/number.py:306
msgid "five"
msgstr "пять"

#: src/humanize/number.py:307
msgid "six"
msgstr "шесть"

#: src/humanize/number.py:308
msgid "seven"
msgstr "семь"

#: src/humanize/number.py:309
msgid "eight"
msgstr "восемь"

#: src/humanize/number.py:310
msgid "nine"
msgstr "девять"

#: src/humanize/time.py:152
#, python-format
msgid "%d microsecond"
msgid_plural "%d microseconds"
msgstr[0] "%d микросекунда"
msgstr[1] "%d микросекунды"
msgstr[2] "%d микросекунд"

#: src/humanize/time.py:161
#, python-format
msgid "%d millisecond"
msgid_plural "%d milliseconds"
msgstr[0] "%d миллисекунда"
msgstr[1] "%d миллисекунды"
msgstr[2] "%d миллисекунд"

#: src/humanize/time.py:164 src/humanize/time.py:259
msgid "a moment"
msgstr "только что"

#: src/humanize/time.py:167
msgid "a second"
msgstr "секунду"

#: src/humanize/time.py:170
#, python-format
msgid "%d second"
msgid_plural "%d seconds"
msgstr[0] "%d секунда"
msgstr[1] "%d секунды"
msgstr[2] "%d секунд"

#: src/humanize/time.py:173
msgid "a minute"
msgstr "минуту"

#: src/humanize/time.py:177
#, python-format
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] "%d минута"
msgstr[1] "%d минуты"
msgstr[2] "%d минут"

#: src/humanize/time.py:180
msgid "an hour"
msgstr "час"

#: src/humanize/time.py:184
#, python-format
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] "%d час"
msgstr[1] "%d часа"
msgstr[2] "%d часов"

#: src/humanize/time.py:188
msgid "a day"
msgstr "день"

#: src/humanize/time.py:191 src/humanize/time.py:194
#, python-format
msgid "%d day"
msgid_plural "%d days"
msgstr[0] "%d день"
msgstr[1] "%d дня"
msgstr[2] "%d дней"

#: src/humanize/time.py:197
msgid "a month"
msgstr "месяц"

#: src/humanize/time.py:199
#, python-format
msgid "%d month"
msgid_plural "%d months"
msgstr[0] "%d месяц"
msgstr[1] "%d месяца"
msgstr[2] "%d месяцев"

#: src/humanize/time.py:203
msgid "a year"
msgstr "год"

#: src/humanize/time.py:206 src/humanize/time.py:217
#, python-format
msgid "1 year, %d day"
msgid_plural "1 year, %d days"
msgstr[0] "1 год, %d день"
msgstr[1] "1 год, %d дня"
msgstr[2] "1 год, %d дней"

#: src/humanize/time.py:210
msgid "1 year, 1 month"
msgstr "1 год, 1 месяц"

#: src/humanize/time.py:213
#, python-format
msgid "1 year, %d month"
msgid_plural "1 year, %d months"
msgstr[0] "1 год, %d месяц"
msgstr[1] "1 год, %d месяца"
msgstr[2] "1 год, %d месяцев"

#: src/humanize/time.py:219
#, python-format
msgid "%d year"
msgid_plural "%d years"
msgstr[0] "%d год"
msgstr[1] "%d года"
msgstr[2] "%d лет"

#: src/humanize/time.py:256
#, python-format
msgid "%s from now"
msgstr "через %s"

#: src/humanize/time.py:256
#, python-format
msgid "%s ago"
msgstr "%s назад"

#: src/humanize/time.py:260
msgid "now"
msgstr "сейчас"

#: src/humanize/time.py:284
msgid "today"
msgstr "сегодня"

#: src/humanize/time.py:287
msgid "tomorrow"
msgstr "завтра"

#: src/humanize/time.py:290
msgid "yesterday"
msgstr "вчера"

#: src/humanize/time.py:600
#, python-format
msgid "%s and %s"
msgstr "%s и %s"
