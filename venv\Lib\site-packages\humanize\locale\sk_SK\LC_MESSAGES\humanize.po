# Slovak translation of humanize
# Copyright (C) 2016
# This file is distributed under the same license as the PACKAGE package.
# <PERSON> <jose1711 gmail com>, 2016.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: humanize\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-08 19:22+0200\n"
"PO-Revision-Date: 2020-09-29 22:43+0300\n"
"Last-Translator: <PERSON> <jose1711 gmail com>\n"
"Language-Team: sk <<EMAIL>>\n"
"Language: Slovak\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n==1) ? 0 : (n>=2 && n<=4) ? 1 : 2;\n"

#: src/humanize/number.py:84
msgctxt "0 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:85
msgctxt "1 (male)"
msgid "st"
msgstr "."

#: src/humanize/number.py:86
msgctxt "2 (male)"
msgid "nd"
msgstr "."

#: src/humanize/number.py:87
msgctxt "3 (male)"
msgid "rd"
msgstr "."

#: src/humanize/number.py:88
msgctxt "4 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:89
msgctxt "5 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:90
msgctxt "6 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:91
msgctxt "7 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:92
msgctxt "8 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:93
msgctxt "9 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:97
msgctxt "0 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:98
msgctxt "1 (female)"
msgid "st"
msgstr "."

#: src/humanize/number.py:99
msgctxt "2 (female)"
msgid "nd"
msgstr "."

#: src/humanize/number.py:100
msgctxt "3 (female)"
msgid "rd"
msgstr "."

#: src/humanize/number.py:101
msgctxt "4 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:102
msgctxt "5 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:103
msgctxt "6 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:104
msgctxt "7 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:105
msgctxt "8 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:106
msgctxt "9 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:178
msgid "thousand"
msgid_plural "thousand"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: src/humanize/number.py:179
msgid "million"
msgid_plural "million"
msgstr[0] "milióna/ov"
msgstr[1] "milióna/ov"
msgstr[2] "milióna/ov"

#: src/humanize/number.py:180
msgid "billion"
msgid_plural "billion"
msgstr[0] "miliardy/árd"
msgstr[1] "miliardy/árd"
msgstr[2] "miliardy/árd"

#: src/humanize/number.py:181
msgid "trillion"
msgid_plural "trillion"
msgstr[0] "bilióna/ov"
msgstr[1] "bilióna/ov"
msgstr[2] "bilióna/ov"

#: src/humanize/number.py:182
msgid "quadrillion"
msgid_plural "quadrillion"
msgstr[0] "biliardy/árd"
msgstr[1] "biliardy/árd"
msgstr[2] "biliardy/árd"

#: src/humanize/number.py:183
msgid "quintillion"
msgid_plural "quintillion"
msgstr[0] "trilióna/árd"
msgstr[1] "trilióna/árd"
msgstr[2] "trilióna/árd"

#: src/humanize/number.py:184
msgid "sextillion"
msgid_plural "sextillion"
msgstr[0] "triliardy/árd"
msgstr[1] "triliardy/árd"
msgstr[2] "triliardy/árd"

#: src/humanize/number.py:185
msgid "septillion"
msgid_plural "septillion"
msgstr[0] "kvadrilióna/ov"
msgstr[1] "kvadrilióna/ov"
msgstr[2] "kvadrilióna/ov"

#: src/humanize/number.py:186
msgid "octillion"
msgid_plural "octillion"
msgstr[0] "kvadriliardy/árd"
msgstr[1] "kvadriliardy/árd"
msgstr[2] "kvadriliardy/árd"

#: src/humanize/number.py:187
msgid "nonillion"
msgid_plural "nonillion"
msgstr[0] "kvintilióna/ov"
msgstr[1] "kvintilióna/ov"
msgstr[2] "kvintilióna/ov"

#: src/humanize/number.py:188
msgid "decillion"
msgid_plural "decillion"
msgstr[0] "kvintiliardy/árd"
msgstr[1] "kvintiliardy/árd"
msgstr[2] "kvintiliardy/árd"

#: src/humanize/number.py:189
msgid "googol"
msgid_plural "googol"
msgstr[0] "googola/ov"
msgstr[1] "googola/ov"
msgstr[2] "googola/ov"

#: src/humanize/number.py:301
msgid "zero"
msgstr "nula"

#: src/humanize/number.py:302
msgid "one"
msgstr "jedna"

#: src/humanize/number.py:303
msgid "two"
msgstr "dve"

#: src/humanize/number.py:304
msgid "three"
msgstr "tri"

#: src/humanize/number.py:305
msgid "four"
msgstr "štyri"

#: src/humanize/number.py:306
msgid "five"
msgstr "päť"

#: src/humanize/number.py:307
msgid "six"
msgstr "šesť"

#: src/humanize/number.py:308
msgid "seven"
msgstr "sedem"

#: src/humanize/number.py:309
msgid "eight"
msgstr "osem"

#: src/humanize/number.py:310
msgid "nine"
msgstr "deväť"

#: src/humanize/time.py:152
#, fuzzy, python-format
msgid "%d microsecond"
msgid_plural "%d microseconds"
msgstr[0] "%d mikrosekundu"
msgstr[1] "%d mikrosekundy"
msgstr[2] "%d mikrosekúnd"

#: src/humanize/time.py:161
#, fuzzy, python-format
msgid "%d millisecond"
msgid_plural "%d milliseconds"
msgstr[0] "%d milisekundu"
msgstr[1] "%d milisekundy"
msgstr[2] "%d milisekúnd"

#: src/humanize/time.py:164 src/humanize/time.py:259
msgid "a moment"
msgstr "chvíľku"

#: src/humanize/time.py:167
msgid "a second"
msgstr "sekundu"

#: src/humanize/time.py:170
#, python-format
msgid "%d second"
msgid_plural "%d seconds"
msgstr[0] "%d sekundu"
msgstr[1] "%d sekundy"
msgstr[2] "%d sekúnd"

#: src/humanize/time.py:173
msgid "a minute"
msgstr "minútu"

#: src/humanize/time.py:177
#, python-format
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] "%d minútu"
msgstr[1] "%d minúty"
msgstr[2] "%d minút"

#: src/humanize/time.py:180
msgid "an hour"
msgstr "hodinu"

#: src/humanize/time.py:184
#, python-format
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] "%d hodina"
msgstr[1] "%d hodiny"
msgstr[2] "%d hodín"

#: src/humanize/time.py:188
msgid "a day"
msgstr "deň"

#: src/humanize/time.py:191 src/humanize/time.py:194
#, python-format
msgid "%d day"
msgid_plural "%d days"
msgstr[0] "%d deň"
msgstr[1] "%d dni"
msgstr[2] "%d dní"

#: src/humanize/time.py:197
msgid "a month"
msgstr "mesiac"

#: src/humanize/time.py:199
#, python-format
msgid "%d month"
msgid_plural "%d months"
msgstr[0] "%d mesiac"
msgstr[1] "%d mesiace"
msgstr[2] "%d mesiacov"

#: src/humanize/time.py:203
msgid "a year"
msgstr "rok"

#: src/humanize/time.py:206 src/humanize/time.py:217
#, python-format
msgid "1 year, %d day"
msgid_plural "1 year, %d days"
msgstr[0] "1 rok, %d deň"
msgstr[1] "1 rok, %d dni"
msgstr[2] "1 rok, %d dní"

#: src/humanize/time.py:210
msgid "1 year, 1 month"
msgstr "1 rok, 1 mesiac"

#: src/humanize/time.py:213
#, python-format
msgid "1 year, %d month"
msgid_plural "1 year, %d months"
msgstr[0] "1 rok, %d mesiac"
msgstr[1] "1 rok, %d mesiace"
msgstr[2] "1 rok, %d mesiacov"

#: src/humanize/time.py:219
#, python-format
msgid "%d year"
msgid_plural "%d years"
msgstr[0] "%d rok"
msgstr[1] "%d roky"
msgstr[2] "%d rokov"

#: src/humanize/time.py:256
#, python-format
msgid "%s from now"
msgstr "o %s"

#: src/humanize/time.py:256
#, python-format
msgid "%s ago"
msgstr "%s naspäť"

#: src/humanize/time.py:260
msgid "now"
msgstr "teraz"

#: src/humanize/time.py:284
msgid "today"
msgstr "dnes"

#: src/humanize/time.py:287
msgid "tomorrow"
msgstr "zajtra"

#: src/humanize/time.py:290
msgid "yesterday"
msgstr "včera"

#: src/humanize/time.py:600
#, python-format
msgid "%s and %s"
msgstr ""
