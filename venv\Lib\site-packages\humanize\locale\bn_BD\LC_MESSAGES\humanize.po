# Bengali translations for humanize package.
# Copyright (C) 2021 THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the humanize package.
# <AUTHOR> <EMAIL>, 2021.
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
#
msgid ""
msgstr ""
"Project-Id-Version: humanize\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-08 19:22+0200\n"
"PO-Revision-Date: 2021-07-29 22:07+0600\n"
"Last-Translator: U-WASI-PC\\Wasi Master <<EMAIL>>\n"
"Language-Team: Bengali\n"
"Language: bn_BD\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: src/humanize/number.py:84
msgctxt "0 (male)"
msgid "th"
msgstr "তম"

#: src/humanize/number.py:85
msgctxt "1 (male)"
msgid "st"
msgstr "তম"

#: src/humanize/number.py:86
msgctxt "2 (male)"
msgid "nd"
msgstr "তম"

#: src/humanize/number.py:87
msgctxt "3 (male)"
msgid "rd"
msgstr "তম"

#: src/humanize/number.py:88
msgctxt "4 (male)"
msgid "th"
msgstr "তম"

#: src/humanize/number.py:89
msgctxt "5 (male)"
msgid "th"
msgstr "তম"

#: src/humanize/number.py:90
msgctxt "6 (male)"
msgid "th"
msgstr "তম"

#: src/humanize/number.py:91
msgctxt "7 (male)"
msgid "th"
msgstr "তম"

#: src/humanize/number.py:92
msgctxt "8 (male)"
msgid "th"
msgstr "তম"

#: src/humanize/number.py:93
msgctxt "9 (male)"
msgid "th"
msgstr "তম"

#: src/humanize/number.py:97
msgctxt "0 (female)"
msgid "th"
msgstr "তম"

#: src/humanize/number.py:98
msgctxt "1 (female)"
msgid "st"
msgstr "তম"

#: src/humanize/number.py:99
msgctxt "2 (female)"
msgid "nd"
msgstr "তম"

#: src/humanize/number.py:100
msgctxt "3 (female)"
msgid "rd"
msgstr "তম"

#: src/humanize/number.py:101
msgctxt "4 (female)"
msgid "th"
msgstr "তম"

#: src/humanize/number.py:102
msgctxt "5 (female)"
msgid "th"
msgstr "তম"

#: src/humanize/number.py:103
msgctxt "6 (female)"
msgid "th"
msgstr "তম"

#: src/humanize/number.py:104
msgctxt "7 (female)"
msgid "th"
msgstr "তম"

#: src/humanize/number.py:105
msgctxt "8 (female)"
msgid "th"
msgstr "তম"

#: src/humanize/number.py:106
msgctxt "9 (female)"
msgid "th"
msgstr "তম"

#: src/humanize/number.py:178
msgid "thousand"
msgid_plural "thousand"
msgstr[0] "%d হাজার"
msgstr[1] "%d হাজার"

#: src/humanize/number.py:179
#, fuzzy
msgid "million"
msgid_plural "million"
msgstr[0] "%d মিলিয়ন"
msgstr[1] "%d মিলিয়ন"

#: src/humanize/number.py:180
msgid "billion"
msgid_plural "billion"
msgstr[0] "%d বিলিয়ন"
msgstr[1] "%d বিলিয়ন"

#: src/humanize/number.py:181
msgid "trillion"
msgid_plural "trillion"
msgstr[0] "%d ট্রিলিয়ন"
msgstr[1] "%d ট্রিলিয়ন"

#: src/humanize/number.py:182
msgid "quadrillion"
msgid_plural "quadrillion"
msgstr[0] "%d কোয়াড্রিলিয়ন"
msgstr[1] "%d কোয়াড্রিলিয়ন"

#: src/humanize/number.py:183
msgid "quintillion"
msgid_plural "quintillion"
msgstr[0] "%d কুইন্টিলিয়ন"
msgstr[1] "%d কুইন্টিলিয়ন"

#: src/humanize/number.py:184
msgid "sextillion"
msgid_plural "sextillion"
msgstr[0] "%d সেক্সটিলিয়ন"
msgstr[1] "%d সেক্সটিলিয়ন"

#: src/humanize/number.py:185
msgid "septillion"
msgid_plural "septillion"
msgstr[0] "%d সেপটিলিয়ন"
msgstr[1] "%d সেপটিলিয়ন"

#: src/humanize/number.py:186
msgid "octillion"
msgid_plural "octillion"
msgstr[0] "%d ওকটিলিয়ন"
msgstr[1] "%d ওকটিলিয়ন"

#: src/humanize/number.py:187
msgid "nonillion"
msgid_plural "nonillion"
msgstr[0] ""
msgstr[1] ""

#: src/humanize/number.py:188
#, fuzzy
msgid "decillion"
msgid_plural "decillion"
msgstr[0] ""
msgstr[1] ""

#: src/humanize/number.py:189
msgid "googol"
msgid_plural "googol"
msgstr[0] ""
msgstr[1] ""

#: src/humanize/number.py:301
msgid "zero"
msgstr "শুন্য"

#: src/humanize/number.py:302
msgid "one"
msgstr "এক"

#: src/humanize/number.py:303
msgid "two"
msgstr "দুই"

#: src/humanize/number.py:304
msgid "three"
msgstr "তিন"

#: src/humanize/number.py:305
msgid "four"
msgstr "চার"

#: src/humanize/number.py:306
msgid "five"
msgstr "পাঁচ"

#: src/humanize/number.py:307
msgid "six"
msgstr "ছয়"

#: src/humanize/number.py:308
msgid "seven"
msgstr "সাত"

#: src/humanize/number.py:309
msgid "eight"
msgstr "আট"

#: src/humanize/number.py:310
msgid "nine"
msgstr "নয়"

#: src/humanize/time.py:152
#, python-format
msgid "%d microsecond"
msgid_plural "%d microseconds"
msgstr[0] "%d মাইক্রোসেকেন্ড"
msgstr[1] "%d মাইক্রোসেকেন্ড"

#: src/humanize/time.py:161
#, python-format
msgid "%d millisecond"
msgid_plural "%d milliseconds"
msgstr[0] "%d মিলিসেকেন্ড"
msgstr[1] "%d মিলিসেকেন্ড"

#: src/humanize/time.py:164 src/humanize/time.py:259
msgid "a moment"
msgstr "এক মুহুর্ত"

#: src/humanize/time.py:167
msgid "a second"
msgstr "এক সেকেন্ড"

#: src/humanize/time.py:170
#, python-format
msgid "%d second"
msgid_plural "%d seconds"
msgstr[0] "%d সেকেন্ড"
msgstr[1] "%d সেকেন্ড"

#: src/humanize/time.py:173
msgid "a minute"
msgstr "এক মিনিট"

#: src/humanize/time.py:177
#, python-format
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] "%d মিনিট"
msgstr[1] "%d মিনিট"

#: src/humanize/time.py:180
msgid "an hour"
msgstr "এক ঘন্টা"

#: src/humanize/time.py:184
#, python-format
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] "%d ঘন্টা"
msgstr[1] "%d ঘন্টা"

#: src/humanize/time.py:188
msgid "a day"
msgstr "এক দিন"

#: src/humanize/time.py:191 src/humanize/time.py:194
#, python-format
msgid "%d day"
msgid_plural "%d days"
msgstr[0] "%d দিন"
msgstr[1] "%d দিন"

#: src/humanize/time.py:197
msgid "a month"
msgstr "এক মাস"

#: src/humanize/time.py:199
#, python-format
msgid "%d month"
msgid_plural "%d months"
msgstr[0] "%d মাস"
msgstr[1] "%d মাস"

#: src/humanize/time.py:203
msgid "a year"
msgstr "এক বছর"

#: src/humanize/time.py:206 src/humanize/time.py:217
#, python-format
msgid "1 year, %d day"
msgid_plural "1 year, %d days"
msgstr[0] "%d বছর"
msgstr[1] "%d বছর"

#: src/humanize/time.py:210
msgid "1 year, 1 month"
msgstr "১ বছর, ১ মাস"

#: src/humanize/time.py:213
#, python-format
msgid "1 year, %d month"
msgid_plural "1 year, %d months"
msgstr[0] "১ বছর, %d মাস"
msgstr[1] "১ বছর, %d মাস"

#: src/humanize/time.py:219
#, python-format
msgid "%d year"
msgid_plural "%d years"
msgstr[0] "%d বছর"
msgstr[1] "%d বছর"

#: src/humanize/time.py:256
#, python-format
msgid "%s from now"
msgstr "আজ থেকে %s সময় পরে"

#: src/humanize/time.py:256
#, python-format
msgid "%s ago"
msgstr "%s সময় আগে"

#: src/humanize/time.py:260
msgid "now"
msgstr "এখন"

#: src/humanize/time.py:284
msgid "today"
msgstr "আজকে"

#: src/humanize/time.py:287
msgid "tomorrow"
msgstr "আগামীকাল"

#: src/humanize/time.py:290
msgid "yesterday"
msgstr "গতকাল"

#: src/humanize/time.py:600
#, python-format
msgid "%s and %s"
msgstr "%s আর  %s"
