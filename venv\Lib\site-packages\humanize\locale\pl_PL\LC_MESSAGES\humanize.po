# Polish translations for PACKAGE package.
# Copyright (C) 2020 THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2020.
# Added missing strings by <PERSON><PERSON><PERSON><PERSON> <krystian postek eu>, 2020.
# Replace short scale with long scale by <PERSON><PERSON><PERSON> (mjmikulski), 2022.
#
msgid ""
msgstr ""
"Project-Id-Version: 0.0.1\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-08 19:22+0200\n"
"PO-Revision-Date: 2020-04-22 10:02+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON> <bartosz.bubak gmail com>\n"
"Language-Team: Polish\n"
"Language: pl\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n==1 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 "
"|| n%100>=20) ? 1 : 2);\n"

#: src/humanize/number.py:84
msgctxt "0 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:85
msgctxt "1 (male)"
msgid "st"
msgstr "."

#: src/humanize/number.py:86
msgctxt "2 (male)"
msgid "nd"
msgstr "."

#: src/humanize/number.py:87
msgctxt "3 (male)"
msgid "rd"
msgstr "."

#: src/humanize/number.py:88
msgctxt "4 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:89
msgctxt "5 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:90
msgctxt "6 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:91
msgctxt "7 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:92
msgctxt "8 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:93
msgctxt "9 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:97
msgctxt "0 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:98
msgctxt "1 (female)"
msgid "st"
msgstr "."

#: src/humanize/number.py:99
msgctxt "2 (female)"
msgid "nd"
msgstr "."

#: src/humanize/number.py:100
msgctxt "3 (female)"
msgid "rd"
msgstr "."

#: src/humanize/number.py:101
msgctxt "4 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:102
msgctxt "5 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:103
msgctxt "6 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:104
msgctxt "7 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:105
msgctxt "8 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:106
msgctxt "9 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:178
msgid "thousand"
msgid_plural "thousand"
msgstr[0] "tysiąc"
msgstr[1] "tysiąc"
msgstr[2] "tysięcy"

#: src/humanize/number.py:179
msgid "million"
msgid_plural "million"
msgstr[0] "milion"
msgstr[1] "miliony"
msgstr[2] "milionów"

#: src/humanize/number.py:180
msgid "billion"
msgid_plural "billion"
msgstr[0] "miliard"
msgstr[1] "miliardy"
msgstr[2] "miliardów"

#: src/humanize/number.py:181
msgid "trillion"
msgid_plural "trillion"
msgstr[0] "bilion"
msgstr[1] "biliony"
msgstr[2] "bilionów"

#: src/humanize/number.py:182
msgid "quadrillion"
msgid_plural "quadrillion"
msgstr[0] "biliard"
msgstr[1] "biliardy"
msgstr[2] "biliardów"

#: src/humanize/number.py:183
msgid "quintillion"
msgid_plural "quintillion"
msgstr[0] "trylion"
msgstr[1] "tryliony"
msgstr[2] "trylionów"

#: src/humanize/number.py:184
msgid "sextillion"
msgid_plural "sextillion"
msgstr[0] "tryliard"
msgstr[1] "tryliard"
msgstr[2] "tryliard"

#: src/humanize/number.py:185
msgid "septillion"
msgid_plural "septillion"
msgstr[0] "kwadrylion"
msgstr[1] "kwadryliony"
msgstr[2] "kwadrylionów"

#: src/humanize/number.py:186
msgid "octillion"
msgid_plural "octillion"
msgstr[0] "kwadryliard"
msgstr[1] "kwadryliardy"
msgstr[2] "kwadryliardów"

#: src/humanize/number.py:187
msgid "nonillion"
msgid_plural "nonillion"
msgstr[0] "kwintylion"
msgstr[1] "kwintyliony"
msgstr[2] "kwintylionów"

#: src/humanize/number.py:188
msgid "decillion"
msgid_plural "decillion"
msgstr[0] "kwintyliard"
msgstr[1] "kwintyliardy"
msgstr[2] "kwintyliardów"

#: src/humanize/number.py:189
msgid "googol"
msgid_plural "googol"
msgstr[0] "googol"
msgstr[1] "googole"
msgstr[2] "googoli"

#: src/humanize/number.py:301
msgid "zero"
msgstr "zero"

#: src/humanize/number.py:302
msgid "one"
msgstr "jeden"

#: src/humanize/number.py:303
msgid "two"
msgstr "dwa"

#: src/humanize/number.py:304
msgid "three"
msgstr "trzy"

#: src/humanize/number.py:305
msgid "four"
msgstr "cztery"

#: src/humanize/number.py:306
msgid "five"
msgstr "pięć"

#: src/humanize/number.py:307
msgid "six"
msgstr "sześć"

#: src/humanize/number.py:308
msgid "seven"
msgstr "siedem"

#: src/humanize/number.py:309
msgid "eight"
msgstr "osiem"

#: src/humanize/number.py:310
msgid "nine"
msgstr "dziewięć"

#: src/humanize/time.py:152
#, python-format
msgid "%d microsecond"
msgid_plural "%d microseconds"
msgstr[0] "%d mikrosekunda"
msgstr[1] "%d mikrosekundy"
msgstr[2] "%d mikrosekund"

#: src/humanize/time.py:161
#, python-format
msgid "%d millisecond"
msgid_plural "%d milliseconds"
msgstr[0] "%d milisekunda"
msgstr[1] "%d milisekundy"
msgstr[2] "%d milisekund"

#: src/humanize/time.py:164 src/humanize/time.py:259
msgid "a moment"
msgstr "chwila"

#: src/humanize/time.py:167
msgid "a second"
msgstr "sekunda"

#: src/humanize/time.py:170
#, python-format
msgid "%d second"
msgid_plural "%d seconds"
msgstr[0] "%d sekunda"
msgstr[1] "%d sekundy"
msgstr[2] "%d sekund"

#: src/humanize/time.py:173
msgid "a minute"
msgstr "minuta"

#: src/humanize/time.py:177
#, python-format
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] "%d minuta"
msgstr[1] "%d minuty"
msgstr[2] "%d minut"

#: src/humanize/time.py:180
msgid "an hour"
msgstr "godzina"

#: src/humanize/time.py:184
#, python-format
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] "%d godzina"
msgstr[1] "%d godziny"
msgstr[2] "%d godzin"

#: src/humanize/time.py:188
msgid "a day"
msgstr "dzień"

#: src/humanize/time.py:191 src/humanize/time.py:194
#, python-format
msgid "%d day"
msgid_plural "%d days"
msgstr[0] "%d dzień"
msgstr[1] "%d dni"
msgstr[2] "%d dni"

#: src/humanize/time.py:197
msgid "a month"
msgstr "miesiąc"

#: src/humanize/time.py:199
#, python-format
msgid "%d month"
msgid_plural "%d months"
msgstr[0] "%d miesiąc"
msgstr[1] "%d miesiące"
msgstr[2] "%d miesięcy"

#: src/humanize/time.py:203
msgid "a year"
msgstr "rok"

#: src/humanize/time.py:206 src/humanize/time.py:217
#, python-format
msgid "1 year, %d day"
msgid_plural "1 year, %d days"
msgstr[0] "1 rok, %d dzień"
msgstr[1] "1 rok, %d dni"
msgstr[2] "1 rok, %d dni"

#: src/humanize/time.py:210
msgid "1 year, 1 month"
msgstr "1 rok, 1 miesiąc"

#: src/humanize/time.py:213
#, python-format
msgid "1 year, %d month"
msgid_plural "1 year, %d months"
msgstr[0] "1 rok, %d miesiąc"
msgstr[1] "1 rok, %d miesiące"
msgstr[2] "1 rok, %d miesięcy"

#: src/humanize/time.py:219
#, python-format
msgid "%d year"
msgid_plural "%d years"
msgstr[0] "%d rok"
msgstr[1] "%d lata"
msgstr[2] "%d lat"

#: src/humanize/time.py:256
#, python-format
msgid "%s from now"
msgstr "%s od teraz"

#: src/humanize/time.py:256
#, python-format
msgid "%s ago"
msgstr "%s temu"

#: src/humanize/time.py:260
msgid "now"
msgstr "teraz"

#: src/humanize/time.py:284
msgid "today"
msgstr "dziś"

#: src/humanize/time.py:287
msgid "tomorrow"
msgstr "jutro"

#: src/humanize/time.py:290
msgid "yesterday"
msgstr "wczoraj"

#: src/humanize/time.py:600
#, python-format
msgid "%s and %s"
msgstr "%s i %s"
