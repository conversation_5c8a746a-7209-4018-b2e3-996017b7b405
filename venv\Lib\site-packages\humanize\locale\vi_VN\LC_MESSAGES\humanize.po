# Vietnamese (Vietnam) translations for PROJECT.
# Copyright (C) 2013 ORGANIZATION
# This file is distributed under the same license as the PROJECT project.
# <AUTHOR> <EMAIL>, 2013.
#
msgid ""
msgstr ""
"Project-Id-Version: PROJECT VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-08 19:22+0200\n"
"PO-Revision-Date: 2017-05-30 11:51+0700\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: vi_VI <<EMAIL>>\n"
"Language: vi_VN\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"
"Generated-By: Babel 0.9.6\n"
"X-Generator: Poedit 1.8.7.1\n"

#: src/humanize/number.py:84
msgctxt "0 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:85
msgctxt "1 (male)"
msgid "st"
msgstr "."

#: src/humanize/number.py:86
msgctxt "2 (male)"
msgid "nd"
msgstr "."

#: src/humanize/number.py:87
msgctxt "3 (male)"
msgid "rd"
msgstr "."

#: src/humanize/number.py:88
msgctxt "4 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:89
msgctxt "5 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:90
msgctxt "6 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:91
msgctxt "7 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:92
msgctxt "8 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:93
msgctxt "9 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:97
msgctxt "0 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:98
msgctxt "1 (female)"
msgid "st"
msgstr "."

#: src/humanize/number.py:99
msgctxt "2 (female)"
msgid "nd"
msgstr "."

#: src/humanize/number.py:100
msgctxt "3 (female)"
msgid "rd"
msgstr "."

#: src/humanize/number.py:101
msgctxt "4 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:102
msgctxt "5 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:103
msgctxt "6 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:104
msgctxt "7 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:105
msgctxt "8 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:106
msgctxt "9 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:178
msgid "thousand"
msgid_plural "thousand"
msgstr[0] "nghìn"
msgstr[1] "nghìn"

#: src/humanize/number.py:179
msgid "million"
msgid_plural "million"
msgstr[0] "%(value)s triệu"
msgstr[1] "%(value)s triệu"

#: src/humanize/number.py:180
msgid "billion"
msgid_plural "billion"
msgstr[0] "tỷ"
msgstr[1] "tỷ"

#: src/humanize/number.py:181
msgid "trillion"
msgid_plural "trillion"
msgstr[0] "%(value)s nghìn tỷ"
msgstr[1] "%(value)s nghìn tỷ"

#: src/humanize/number.py:182
msgid "quadrillion"
msgid_plural "quadrillion"
msgstr[0] "%(value)s triệu tỷ"
msgstr[1] "%(value)s triệu tỷ"

#: src/humanize/number.py:183
#, fuzzy
msgid "quintillion"
msgid_plural "quintillion"
msgstr[0] "%(value)s tỷ tỷ"
msgstr[1] "%(value)s tỷ tỷ"

#: src/humanize/number.py:184
#, fuzzy
msgid "sextillion"
msgid_plural "sextillion"
msgstr[0] "%(value)s sextillion"
msgstr[1] "%(value)s sextillion"

#: src/humanize/number.py:185
#, fuzzy
msgid "septillion"
msgid_plural "septillion"
msgstr[0] "%(value)s septillion"
msgstr[1] "%(value)s septillion"

#: src/humanize/number.py:186
#, fuzzy
msgid "octillion"
msgid_plural "octillion"
msgstr[0] "%(value)s octillion"
msgstr[1] "%(value)s octillion"

#: src/humanize/number.py:187
#, fuzzy
msgid "nonillion"
msgid_plural "nonillion"
msgstr[0] "%(value)s nonillion"
msgstr[1] "%(value)s nonillion"

#: src/humanize/number.py:188
#, fuzzy
msgid "decillion"
msgid_plural "decillion"
msgstr[0] "%(value)s décillion"
msgstr[1] "%(value)s décillion"

#: src/humanize/number.py:189
#, fuzzy
msgid "googol"
msgid_plural "googol"
msgstr[0] "%(value)s gogol"
msgstr[1] "%(value)s gogol"

#: src/humanize/number.py:301
msgid "zero"
msgstr "không"

#: src/humanize/number.py:302
msgid "one"
msgstr "một"

#: src/humanize/number.py:303
msgid "two"
msgstr "hai"

#: src/humanize/number.py:304
msgid "three"
msgstr "ba"

#: src/humanize/number.py:305
msgid "four"
msgstr "bốn"

#: src/humanize/number.py:306
msgid "five"
msgstr "năm"

#: src/humanize/number.py:307
msgid "six"
msgstr "sáu"

#: src/humanize/number.py:308
msgid "seven"
msgstr "bảy"

#: src/humanize/number.py:309
msgid "eight"
msgstr "tám"

#: src/humanize/number.py:310
msgid "nine"
msgstr "chín"

#: src/humanize/time.py:152
#, python-format
msgid "%d microsecond"
msgid_plural "%d microseconds"
msgstr[0] "%d micro giây"
msgstr[1] "%d micro giây"

#: src/humanize/time.py:161
#, python-format
msgid "%d millisecond"
msgid_plural "%d milliseconds"
msgstr[0] "%d mili giây"
msgstr[1] "%d mili giây"

#: src/humanize/time.py:164 src/humanize/time.py:259
msgid "a moment"
msgstr "ngay lúc này"

#: src/humanize/time.py:167
msgid "a second"
msgstr "một giây"

#: src/humanize/time.py:170
#, python-format
msgid "%d second"
msgid_plural "%d seconds"
msgstr[0] "%d giây"
msgstr[1] "%d giây"

#: src/humanize/time.py:173
msgid "a minute"
msgstr "một phút"

#: src/humanize/time.py:177
#, python-format
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] "%d phút"
msgstr[1] "%d phút"

#: src/humanize/time.py:180
msgid "an hour"
msgstr "một giờ"

#: src/humanize/time.py:184
#, python-format
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] "%d giờ"
msgstr[1] "%d giờ"

#: src/humanize/time.py:188
msgid "a day"
msgstr "một ngày"

#: src/humanize/time.py:191 src/humanize/time.py:194
#, python-format
msgid "%d day"
msgid_plural "%d days"
msgstr[0] "%d ngày"
msgstr[1] "%d ngày"

#: src/humanize/time.py:197
msgid "a month"
msgstr "một tháng"

#: src/humanize/time.py:199
#, python-format
msgid "%d month"
msgid_plural "%d months"
msgstr[0] "%d tháng"
msgstr[1] "%d tháng"

#: src/humanize/time.py:203
msgid "a year"
msgstr "một năm"

#: src/humanize/time.py:206 src/humanize/time.py:217
#, python-format
msgid "1 year, %d day"
msgid_plural "1 year, %d days"
msgstr[0] "1 năm %d ngày"
msgstr[1] "1 năm %d ngày"

#: src/humanize/time.py:210
msgid "1 year, 1 month"
msgstr "1 năm 1 tháng"

#: src/humanize/time.py:213
#, python-format
msgid "1 year, %d month"
msgid_plural "1 year, %d months"
msgstr[0] "1 năm %d tháng"
msgstr[1] "un an et %d mois"

#: src/humanize/time.py:219
#, python-format
msgid "%d year"
msgid_plural "%d years"
msgstr[0] "%d năm"
msgstr[1] "%d năm"

#: src/humanize/time.py:256
#, python-format
msgid "%s from now"
msgstr "%s ngày tới"

#: src/humanize/time.py:256
#, python-format
msgid "%s ago"
msgstr "%s trước"

#: src/humanize/time.py:260
msgid "now"
msgstr "ngay bây giờ"

#: src/humanize/time.py:284
msgid "today"
msgstr "hôm nay"

#: src/humanize/time.py:287
msgid "tomorrow"
msgstr "ngày mai"

#: src/humanize/time.py:290
msgid "yesterday"
msgstr "ngày hôm qua"

#: src/humanize/time.py:600
#, python-format
msgid "%s and %s"
msgstr "%s và %s"
