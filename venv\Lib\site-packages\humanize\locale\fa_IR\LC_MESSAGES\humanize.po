# German translation for humanize.
# Copyright (C) 2016 THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the humanize package.
# <PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: humanize\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-08 19:22+0200\n"
"PO-Revision-Date: 2017-01-10 02:44+0330\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: German\n"
"Language: de\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: <PERSON>\n"
"X-Generator: Poedit 1.5.4\n"

#: src/humanize/number.py:84
msgctxt "0 (male)"
msgid "th"
msgstr "صفرمین"

#: src/humanize/number.py:85
msgctxt "1 (male)"
msgid "st"
msgstr "اولین"

#: src/humanize/number.py:86
msgctxt "2 (male)"
msgid "nd"
msgstr "دومین"

#: src/humanize/number.py:87
msgctxt "3 (male)"
msgid "rd"
msgstr "سومین"

#: src/humanize/number.py:88
msgctxt "4 (male)"
msgid "th"
msgstr "چهارمین"

#: src/humanize/number.py:89
msgctxt "5 (male)"
msgid "th"
msgstr "پنجمین"

#: src/humanize/number.py:90
msgctxt "6 (male)"
msgid "th"
msgstr "ششمین"

#: src/humanize/number.py:91
msgctxt "7 (male)"
msgid "th"
msgstr "هفتمین"

#: src/humanize/number.py:92
msgctxt "8 (male)"
msgid "th"
msgstr "هشتمین"

#: src/humanize/number.py:93
msgctxt "9 (male)"
msgid "th"
msgstr "نهمین"

#: src/humanize/number.py:97
msgctxt "0 (female)"
msgid "th"
msgstr "صفرمین"

#: src/humanize/number.py:98
msgctxt "1 (female)"
msgid "st"
msgstr "اولین"

#: src/humanize/number.py:99
msgctxt "2 (female)"
msgid "nd"
msgstr "دومین"

#: src/humanize/number.py:100
msgctxt "3 (female)"
msgid "rd"
msgstr "سومین"

#: src/humanize/number.py:101
msgctxt "4 (female)"
msgid "th"
msgstr "چهارمین"

#: src/humanize/number.py:102
msgctxt "5 (female)"
msgid "th"
msgstr "پنجمین"

#: src/humanize/number.py:103
msgctxt "6 (female)"
msgid "th"
msgstr "ششمین"

#: src/humanize/number.py:104
msgctxt "7 (female)"
msgid "th"
msgstr "هفتمین"

#: src/humanize/number.py:105
msgctxt "8 (female)"
msgid "th"
msgstr "هشتمین"

#: src/humanize/number.py:106
msgctxt "9 (female)"
msgid "th"
msgstr "نهمین"

#: src/humanize/number.py:178
msgid "thousand"
msgid_plural "thousand"
msgstr[0] "هزار"
msgstr[1] "هزار"

#: src/humanize/number.py:179
msgid "million"
msgid_plural "million"
msgstr[0] "میلیون"
msgstr[1] "میلیون"

#: src/humanize/number.py:180
msgid "billion"
msgid_plural "billion"
msgstr[0] "میلیارد"
msgstr[1] "میلیارد"

#: src/humanize/number.py:181
msgid "trillion"
msgid_plural "trillion"
msgstr[0] "ترلیون"
msgstr[1] "ترلیون"

#: src/humanize/number.py:182
msgid "quadrillion"
msgid_plural "quadrillion"
msgstr[0] "کوادریلیون"
msgstr[1] "کوادریلیون"

#: src/humanize/number.py:183
msgid "quintillion"
msgid_plural "quintillion"
msgstr[0] "کوانتیلیون"
msgstr[1] "کوانتیلیون"

#: src/humanize/number.py:184
msgid "sextillion"
msgid_plural "sextillion"
msgstr[0] "سکستیلیون"
msgstr[1] "سکستیلیون"

#: src/humanize/number.py:185
msgid "septillion"
msgid_plural "septillion"
msgstr[0] "سپتیلیون"
msgstr[1] "سپتیلیون"

#: src/humanize/number.py:186
msgid "octillion"
msgid_plural "octillion"
msgstr[0] "اوکتیلیون"
msgstr[1] "اوکتیلیون"

#: src/humanize/number.py:187
msgid "nonillion"
msgid_plural "nonillion"
msgstr[0] "نونیلیون"
msgstr[1] "نونیلیون"

#: src/humanize/number.py:188
msgid "decillion"
msgid_plural "decillion"
msgstr[0] "دسیلیون"
msgstr[1] "دسیلیون"

#: src/humanize/number.py:189
msgid "googol"
msgid_plural "googol"
msgstr[0] "گوگول"
msgstr[1] "گوگول"

#: src/humanize/number.py:301
msgid "zero"
msgstr "صفر"

#: src/humanize/number.py:302
msgid "one"
msgstr "یک"

#: src/humanize/number.py:303
msgid "two"
msgstr "دو"

#: src/humanize/number.py:304
msgid "three"
msgstr "سه"

#: src/humanize/number.py:305
msgid "four"
msgstr "چهار"

#: src/humanize/number.py:306
msgid "five"
msgstr "پنج"

#: src/humanize/number.py:307
msgid "six"
msgstr "شش"

#: src/humanize/number.py:308
msgid "seven"
msgstr "هفت"

#: src/humanize/number.py:309
msgid "eight"
msgstr "هشت"

#: src/humanize/number.py:310
msgid "nine"
msgstr "نه"

#: src/humanize/time.py:152
#, python-format
msgid "%d microsecond"
msgid_plural "%d microseconds"
msgstr[0] "%d میکرو‌ثانیه"
msgstr[1] "%d میکرو‌ثانیه"

#: src/humanize/time.py:161
#, python-format
msgid "%d millisecond"
msgid_plural "%d milliseconds"
msgstr[0] "%d میلی‌ثانیه"
msgstr[1] "%d میلی‌ثانیه"

#: src/humanize/time.py:164 src/humanize/time.py:259
msgid "a moment"
msgstr "یک لحظه"

#: src/humanize/time.py:167
msgid "a second"
msgstr "یک ثانیه"

#: src/humanize/time.py:170
#, python-format
msgid "%d second"
msgid_plural "%d seconds"
msgstr[0] "%d ثانیه"
msgstr[1] "%d ثانیه"

#: src/humanize/time.py:173
msgid "a minute"
msgstr "یک دقیقه"

#: src/humanize/time.py:177
#, python-format
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] "%d دقیقه"
msgstr[1] "%d دقیقه"

#: src/humanize/time.py:180
msgid "an hour"
msgstr "یک ساعت"

#: src/humanize/time.py:184
#, python-format
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] "%d ساعت"
msgstr[1] "%d ساعت"

#: src/humanize/time.py:188
msgid "a day"
msgstr "یک روز"

#: src/humanize/time.py:191 src/humanize/time.py:194
#, python-format
msgid "%d day"
msgid_plural "%d days"
msgstr[0] "%d روز"
msgstr[1] "%d روز"

#: src/humanize/time.py:197
msgid "a month"
msgstr "یک ماه"

#: src/humanize/time.py:199
#, python-format
msgid "%d month"
msgid_plural "%d months"
msgstr[0] "%d ماه"
msgstr[1] "%d ماه"

#: src/humanize/time.py:203
msgid "a year"
msgstr "یک سال"

#: src/humanize/time.py:206 src/humanize/time.py:217
#, python-format
msgid "1 year, %d day"
msgid_plural "1 year, %d days"
msgstr[0] "۱ سال و %d روز"
msgstr[1] "۱ سال و %d روز"

#: src/humanize/time.py:210
msgid "1 year, 1 month"
msgstr "۱ سال و ۱ ماه"

#: src/humanize/time.py:213
#, python-format
msgid "1 year, %d month"
msgid_plural "1 year, %d months"
msgstr[0] "۱ سال و %d ماه"
msgstr[1] "۱ سال و %d ماه"

#: src/humanize/time.py:219
#, python-format
msgid "%d year"
msgid_plural "%d years"
msgstr[0] "%d سال"
msgstr[1] "%d سال"

#: src/humanize/time.py:256
#, python-format
msgid "%s from now"
msgstr "%s تا به اکنون"

#: src/humanize/time.py:256
#, python-format
msgid "%s ago"
msgstr "%s پیش"

#: src/humanize/time.py:260
msgid "now"
msgstr "اکنون"

#: src/humanize/time.py:284
msgid "today"
msgstr "امروز"

#: src/humanize/time.py:287
msgid "tomorrow"
msgstr "فردا"

#: src/humanize/time.py:290
msgid "yesterday"
msgstr "دیروز"

#: src/humanize/time.py:600
#, python-format
msgid "%s and %s"
msgstr "%s و %s"
