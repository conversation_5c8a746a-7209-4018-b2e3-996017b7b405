# Turkish translation for humanize.
# Copyright (C) 2017 THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the humanize package.
# Emre <PERSON>ay <<EMAIL>>, 2017.
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023.
#
msgid ""
msgstr ""
"Project-Id-Version: humanize\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-08 19:22+0200\n"
"PO-Revision-Date: 2023-08-09 13:12+0300\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Turkish\n"
"Language: tr_TR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.2.2\n"
"Generated-By: <PERSON><PERSON><PERSON>\n"

#: src/humanize/number.py:84
msgctxt "0 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:85
msgctxt "1 (male)"
msgid "st"
msgstr "."

#: src/humanize/number.py:86
msgctxt "2 (male)"
msgid "nd"
msgstr "."

#: src/humanize/number.py:87
msgctxt "3 (male)"
msgid "rd"
msgstr "."

#: src/humanize/number.py:88
msgctxt "4 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:89
msgctxt "5 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:90
msgctxt "6 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:91
msgctxt "7 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:92
msgctxt "8 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:93
msgctxt "9 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:97
msgctxt "0 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:98
msgctxt "1 (female)"
msgid "st"
msgstr "."

#: src/humanize/number.py:99
msgctxt "2 (female)"
msgid "nd"
msgstr "."

#: src/humanize/number.py:100
msgctxt "3 (female)"
msgid "rd"
msgstr "."

#: src/humanize/number.py:101
msgctxt "4 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:102
msgctxt "5 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:103
msgctxt "6 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:104
msgctxt "7 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:105
msgctxt "8 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:106
msgctxt "9 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:178
msgid "thousand"
msgid_plural "thousand"
msgstr[0] "bin"
msgstr[1] "bin"

#: src/humanize/number.py:179
msgid "million"
msgid_plural "million"
msgstr[0] "milyon"
msgstr[1] "milyon"

#: src/humanize/number.py:180
msgid "billion"
msgid_plural "billion"
msgstr[0] "milyar"
msgstr[1] "milyar"

#: src/humanize/number.py:181
msgid "trillion"
msgid_plural "trillion"
msgstr[0] "trilyon"
msgstr[1] "trilyon"

#: src/humanize/number.py:182
msgid "quadrillion"
msgid_plural "quadrillion"
msgstr[0] "katrilyon"
msgstr[1] "katrilyon"

#: src/humanize/number.py:183
msgid "quintillion"
msgid_plural "quintillion"
msgstr[0] "kentilyon"
msgstr[1] "kentilyon"

#: src/humanize/number.py:184
msgid "sextillion"
msgid_plural "sextillion"
msgstr[0] "sekstilyon"
msgstr[1] "sekstilyon"

#: src/humanize/number.py:185
msgid "septillion"
msgid_plural "septillion"
msgstr[0] "septilyon"
msgstr[1] "septilyon"

#: src/humanize/number.py:186
msgid "octillion"
msgid_plural "octillion"
msgstr[0] "oktilyon"
msgstr[1] "oktilyon"

#: src/humanize/number.py:187
msgid "nonillion"
msgid_plural "nonillion"
msgstr[0] "nonilyon"
msgstr[1] "nonilyon"

#: src/humanize/number.py:188
msgid "decillion"
msgid_plural "decillion"
msgstr[0] "desilyon"
msgstr[1] "desilyon"

#: src/humanize/number.py:189
msgid "googol"
msgid_plural "googol"
msgstr[0] "googol"
msgstr[1] "googol"

#: src/humanize/number.py:301
msgid "zero"
msgstr "sıfır"

#: src/humanize/number.py:302
msgid "one"
msgstr "bir"

#: src/humanize/number.py:303
msgid "two"
msgstr "iki"

#: src/humanize/number.py:304
msgid "three"
msgstr "üç"

#: src/humanize/number.py:305
msgid "four"
msgstr "dört"

#: src/humanize/number.py:306
msgid "five"
msgstr "beş"

#: src/humanize/number.py:307
msgid "six"
msgstr "altı"

#: src/humanize/number.py:308
msgid "seven"
msgstr "yedi"

#: src/humanize/number.py:309
msgid "eight"
msgstr "sekiz"

#: src/humanize/number.py:310
msgid "nine"
msgstr "dokuz"

#: src/humanize/time.py:152
#, python-format
msgid "%d microsecond"
msgid_plural "%d microseconds"
msgstr[0] "%d mikrosaniye"
msgstr[1] "%d mikrosaniye"

#: src/humanize/time.py:161
#, python-format
msgid "%d millisecond"
msgid_plural "%d milliseconds"
msgstr[0] "%d milisaniye"
msgstr[1] "%d milisaniye"

# The phrase "biraz"  doesn't make sense as time expression in Turkish without suffix "önce" (ago) in this case. I think one second [ago] (bir saniye [önce]) is more appropriate here.
#: src/humanize/time.py:164 src/humanize/time.py:259
msgid "a moment"
msgstr "bir saniye"

#: src/humanize/time.py:167
msgid "a second"
msgstr "bir saniye"

#: src/humanize/time.py:170
#, python-format
msgid "%d second"
msgid_plural "%d seconds"
msgstr[0] "%d saniye"
msgstr[1] "%d saniye"

#: src/humanize/time.py:173
msgid "a minute"
msgstr "bir dakika"

#: src/humanize/time.py:177
#, python-format
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] "%d dakika"
msgstr[1] "%d dakika"

#: src/humanize/time.py:180
msgid "an hour"
msgstr "bir saat"

#: src/humanize/time.py:184
#, python-format
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] "%d saat"
msgstr[1] "%d saat"

#: src/humanize/time.py:188
msgid "a day"
msgstr "bir gün"

#: src/humanize/time.py:191 src/humanize/time.py:194
#, python-format
msgid "%d day"
msgid_plural "%d days"
msgstr[0] "%d gün"
msgstr[1] "%d gün"

#: src/humanize/time.py:197
msgid "a month"
msgstr "bir ay"

#: src/humanize/time.py:199
#, python-format
msgid "%d month"
msgid_plural "%d months"
msgstr[0] "%d ay"
msgstr[1] "%d ay"

#: src/humanize/time.py:203
msgid "a year"
msgstr "bir yıl"

#: src/humanize/time.py:206 src/humanize/time.py:217
#, python-format
msgid "1 year, %d day"
msgid_plural "1 year, %d days"
msgstr[0] "1 yıl, %d gün"
msgstr[1] "1 yıl, %d gün"

#: src/humanize/time.py:210
msgid "1 year, 1 month"
msgstr "1 yıl, 1 ay"

#: src/humanize/time.py:213
#, python-format
msgid "1 year, %d month"
msgid_plural "1 year, %d months"
msgstr[0] "1 yıl, %d ay"
msgstr[1] "1 yıl, %d ay"

#: src/humanize/time.py:219
#, python-format
msgid "%d year"
msgid_plural "%d years"
msgstr[0] "%d yıl"
msgstr[1] "%d yıl"

#: src/humanize/time.py:256
#, python-format
msgid "%s from now"
msgstr "şu andan itibaren %s"

#: src/humanize/time.py:256
#, python-format
msgid "%s ago"
msgstr "%s önce"

#: src/humanize/time.py:260
msgid "now"
msgstr "şimdi"

#: src/humanize/time.py:284
msgid "today"
msgstr "bugün"

#: src/humanize/time.py:287
msgid "tomorrow"
msgstr "yarın"

#: src/humanize/time.py:290
msgid "yesterday"
msgstr "dün"

#: src/humanize/time.py:600
#, python-format
msgid "%s and %s"
msgstr "%s ve %s"
