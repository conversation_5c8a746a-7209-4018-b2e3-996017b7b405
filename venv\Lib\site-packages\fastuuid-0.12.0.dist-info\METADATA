Metadata-Version: 2.4
Name: fastuuid
Version: 0.12.0
License-File: LICENSE
Summary: Python bindings to Rust's UUID library.
Author: <PERSON><PERSON> <<EMAIL>>
Author-email: <PERSON><PERSON> <<EMAIL>>
License: BSD3
Requires-Python: >=3.8
Description-Content-Type: text/x-rst; charset=UTF-8
Project-URL: homepage, https://github.com/thedrow/fastuuid/

fastuuid
========

.. image:: https://travis-ci.com/thedrow/fastuuid.svg?branch=master
    :target: https://travis-ci.com/thedrow/fastuuid

FastUUID is a library which provides CPython bindings to <PERSON><PERSON>'s UUID library.

The provided API is exactly as Python's builtin UUID class.

It is supported on Python 3.8, 3.9, 3.10, 3.11, 3.12, & 3.13.

Why?
----

It is much faster than Python's pure-python implementation and it is stricter
when parsing hexadecimal representation of UUIDs.

If you need to generate a lot of random UUIDs we also provide the `uuid4_bulk()`
function which releases the GIL for the entire duration of the generation.
This allows other threads to run while the library generates UUIDs.

Benchmarks
----------

===========  =========  =================  =======================  ===============================  ================  ====================  =================  ========  =========================================
processor    machine    python compiler    python implementation    python implementation version    python version    python build          release            system    cpu
===========  =========  =================  =======================  ===============================  ================  ====================  =================  ========  =========================================
x86_64       x86_64     GCC 5.5.0          CPython                  3.7.2                            3.7.2             default               4.15.0-50-generic  Linux     Intel(R) Core(TM) i7-7700HQ CPU @ 2.80GHz
===========  =========  =================  =======================  ===============================  ================  ====================  =================  ========  =========================================


=======================================================  ======================  ======================  ======================  ======================  ======================  ======================  ==========  ==================  ======  ==========
name                                                     min                     max                     mean                    stddev                  median                  iqr                     outliers    ops                 rounds  iterations
=======================================================  ======================  ======================  ======================  ======================  ======================  ======================  ==========  ==================  ======  ==========
tests/test_benchmarks.py::test_parse_bytes_fastuuid      8.770148269832134e-07   3.0054012313485146e-05  9.848993185755912e-07   6.654121944953314e-07   9.530049283057451e-07   2.6979250833392143e-08  515;8080    1015332.2082162144  149366  1
tests/test_benchmarks.py::test_parse_bytes_uuid          9.00006853044033e-07    2.4181994376704097e-05  1.0102117337399724e-06  6.361040394445994e-07   9.739887900650501e-07   3.899913281202316e-08   1130;10702  989891.4916557473   198020  1
tests/test_benchmarks.py::test_parse_bytes_le_fastuuid   9.00996383279562e-07    2.4662993382662535e-05  1.0116569599011118e-06  5.687526428398989e-07   9.840005077421665e-07   2.200249582529068e-08   703;9368    988477.3590622543   163052  1
tests/test_benchmarks.py::test_parse_bytes_le_uuid       1.348991645500064e-06   3.5200006095692515e-05  1.5184524591452776e-06  9.295692916442362e-07   1.448992406949401e-06   3.897002898156643e-08   1620;12511  658565.2346092485   170271  1
tests/test_benchmarks.py::test_parse_fields_fastuuid     9.819923434406519e-07   3.2625976018607616e-05  1.217285795660234e-06   1.0234898538816672e-06  1.087988493964076e-06   6.702612154185772e-08   3199;12487  821499.7690477591   143844  1
tests/test_benchmarks.py::test_parse_fields_uuid         1.1137977708131076e-06  0.000147809402551502    1.2054474234359692e-06  5.093104655522965e-07   1.144595444202423e-06   6.060581654310231e-08   2304;5896   829567.4954861335   167983  5
tests/test_benchmarks.py::test_parse_hex_fastuuid        9.870273061096668e-07   2.906599547713995e-05   1.11212962918218e-06    6.906885628642859e-07   1.0759977158159018e-06  3.0995579436421394e-08  577;8272    899175.7559191765   143288  1
tests/test_benchmarks.py::test_parse_hex_uuid            1.3360113371163607e-06  2.6262016035616398e-05  1.4448148991822913e-06  7.064083638385458e-07   1.3989920262247324e-06  2.9016518965363503e-08  679;4802    692130.1826039868   82156   1
tests/test_benchmarks.py::test_parse_int_uuid            5.448004230856896e-07   4.164349229540676e-06   6.099919819231937e-07   2.0401652680352933e-07  5.548994522541762e-07   4.430039552971725e-08   3607;3925   1639365.8107557097  87951   20
tests/test_benchmarks.py::test_parse_int_fastuuid        8.950009942054749e-07   4.946498665958643e-05   1.0105578493921953e-06  6.873330198387691e-07   9.739887900650501e-07   2.1012965589761734e-08  529;12534   989552.4542226401   176088  1
tests/test_benchmarks.py::test_fast_uuidv3               5.410998710431158e-07   3.5570512409321965e-06  5.971385425220447e-07   1.672736409563351e-07   5.526497261598707e-07   2.949964255094524e-08   4865;6332   1674653.248434526   83508   20
tests/test_benchmarks.py::test_uuidv3                    3.6269775591790676e-06  4.193797940388322e-05   3.933511159797234e-06   1.4521217506191846e-06  3.782013664022088e-06   6.00120984017849e-08    548;4193    254225.79455743768  53582   1
tests/test_benchmarks.py::test_fast_uuidv4               1.47343598655425e-07    2.069187758024782e-06   1.6777362874701377e-07  7.169360028617447e-08   1.5453133528353646e-07  8.188180800061673e-09   6101;11550  5960412.297619802   198413  32
tests/test_benchmarks.py::test_uuidv4                    2.275977749377489e-06   5.939402035437524e-05   2.5699563458422217e-06  1.316784132061215e-06   2.38200300373137e-06    1.309963408857584e-07   2068;5815   389111.667837409    85610   1
tests/test_benchmarks.py::test_fast_uuidv4_bulk_threads  0.0009843519947025925   0.007268004992511123    0.0014418828965801719   0.0007545185495019851   0.0012059269938617945   0.0003288870066171512   42;54       693.5375975204223   549     1
tests/test_benchmarks.py::test_fast_uuidv4_threads       0.0030693279986735433   0.008087011985480785    0.004009611603774935    0.000715605913448762    0.0038650799833703786   0.0006588477554032579   53;19       249.40071478707026  273     1
tests/test_benchmarks.py::test_uuidv4_threads            0.030999513022834435    0.06895541000994854     0.040025271589084616    0.009975862168373506    0.036475206492468715    0.008713199000339955    3;2         24.98421522947798   22      1
tests/test_benchmarks.py::test_fast_uuidv5               5.316498572938144e-07   4.090600123163313e-06   5.890041556925782e-07   1.8620985914996815e-07  5.419497028924525e-07   2.9799412004649576e-08  3998;6415   1697780.8905680121  88921   20
tests/test_benchmarks.py::test_uuidv5                    3.7190038710832596e-06  5.8079982409253716e-05  4.403547300216035e-06   2.439066121654033e-06   3.910012310370803e-06   2.169981598854065e-07   2283;4139   227089.64655629804  57383   1
=======================================================  ======================  ======================  ======================  ======================  ======================  ======================  ==========  ==================  ======  ==========

Run them yourself to verify.

PRs are welcome.

