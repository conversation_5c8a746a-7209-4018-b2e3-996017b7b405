# Hungarian translations for humanize package.
# Copyright (C) 2023
# This file is distributed under the same license as the humanize project.
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023.
#
msgid ""
msgstr ""
"Project-Id-Version: humanize\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-08 19:22+0200\n"
"PO-Revision-Date: 2021-07-05 10:30+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Hungarian\n"
"Language: hu\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: src/humanize/number.py:84
msgctxt "0 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:85
msgctxt "1 (male)"
msgid "st"
msgstr "."

#: src/humanize/number.py:86
msgctxt "2 (male)"
msgid "nd"
msgstr "."

#: src/humanize/number.py:87
msgctxt "3 (male)"
msgid "rd"
msgstr "."

#: src/humanize/number.py:88
msgctxt "4 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:89
msgctxt "5 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:90
msgctxt "6 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:91
msgctxt "7 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:92
msgctxt "8 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:93
msgctxt "9 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:97
msgctxt "0 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:98
msgctxt "1 (female)"
msgid "st"
msgstr "."

#: src/humanize/number.py:99
msgctxt "2 (female)"
msgid "nd"
msgstr "."

#: src/humanize/number.py:100
msgctxt "3 (female)"
msgid "rd"
msgstr "."

#: src/humanize/number.py:101
msgctxt "4 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:102
msgctxt "5 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:103
msgctxt "6 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:104
msgctxt "7 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:105
msgctxt "8 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:106
msgctxt "9 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:178
msgid "thousand"
msgid_plural "thousand"
msgstr[0] "ezer"
msgstr[1] "ezer"

#: src/humanize/number.py:179
msgid "million"
msgid_plural "million"
msgstr[0] "millió"
msgstr[1] "millió"

#: src/humanize/number.py:180
msgid "billion"
msgid_plural "billion"
msgstr[0] "milliárd"
msgstr[1] "milliárd"

#: src/humanize/number.py:181
msgid "trillion"
msgid_plural "trillion"
msgstr[0] "billió"
msgstr[1] "billió"

#: src/humanize/number.py:182
msgid "quadrillion"
msgid_plural "quadrillion"
msgstr[0] "kvadrillió"
msgstr[1] "kvadrillió"

#: src/humanize/number.py:183
msgid "quintillion"
msgid_plural "quintillion"
msgstr[0] "kvadrilliárd"
msgstr[1] "kvadrilliárd"

#: src/humanize/number.py:184
msgid "sextillion"
msgid_plural "sextillion"
msgstr[0] "trilliárd"
msgstr[1] "trilliárd"

#: src/humanize/number.py:185
msgid "septillion"
msgid_plural "septillion"
msgstr[0] "kvadrillió"
msgstr[1] "kvadrillió"

#: src/humanize/number.py:186
msgid "octillion"
msgid_plural "octillion"
msgstr[0] "kvadrilliárd"
msgstr[1] "kvadrilliárd"

#: src/humanize/number.py:187
msgid "nonillion"
msgid_plural "nonillion"
msgstr[0] "kvintillió"
msgstr[1] "kvintillió"

#: src/humanize/number.py:188
msgid "decillion"
msgid_plural "decillion"
msgstr[0] "kvintilliárd"
msgstr[1] "kvintilliárd"

#: src/humanize/number.py:189
msgid "googol"
msgid_plural "googol"
msgstr[0] "tízszexdecilliárd"
msgstr[1] "tízszexdecilliárd"

#: src/humanize/number.py:301
msgid "zero"
msgstr "nulla"

#: src/humanize/number.py:302
msgid "one"
msgstr "egy"

#: src/humanize/number.py:303
msgid "two"
msgstr "kettő"

#: src/humanize/number.py:304
msgid "three"
msgstr "három"

#: src/humanize/number.py:305
msgid "four"
msgstr "négy"

#: src/humanize/number.py:306
msgid "five"
msgstr "öt"

#: src/humanize/number.py:307
msgid "six"
msgstr "hat"

#: src/humanize/number.py:308
msgid "seven"
msgstr "hét"

#: src/humanize/number.py:309
msgid "eight"
msgstr "nyolc"

#: src/humanize/number.py:310
msgid "nine"
msgstr "kilenc"

#: src/humanize/time.py:152
#, fuzzy, python-format
msgid "%d microsecond"
msgid_plural "%d microseconds"
msgstr[0] "%d mikroszekundum"
msgstr[1] "%d mikroszekundum"

#: src/humanize/time.py:161
#, fuzzy, python-format
msgid "%d millisecond"
msgid_plural "%d milliseconds"
msgstr[0] "%d miliszekundum"
msgstr[1] "%d miliszekundum"

#: src/humanize/time.py:164 src/humanize/time.py:259
msgid "a moment"
msgstr "egy pillanat"

#: src/humanize/time.py:167
msgid "a second"
msgstr "egy másodperc"

#: src/humanize/time.py:170
#, python-format
msgid "%d second"
msgid_plural "%d seconds"
msgstr[0] "%d másodperc"
msgstr[1] "%d másodperc"

#: src/humanize/time.py:173
msgid "a minute"
msgstr "egy perc"

#: src/humanize/time.py:177
#, python-format
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] "%d perc"
msgstr[1] "%d perc"

#: src/humanize/time.py:180
msgid "an hour"
msgstr "egy óra"

#: src/humanize/time.py:184
#, python-format
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] "%d óra"
msgstr[1] "%d óra"

#: src/humanize/time.py:188
msgid "a day"
msgstr "egy nap"

#: src/humanize/time.py:191 src/humanize/time.py:194
#, python-format
msgid "%d day"
msgid_plural "%d days"
msgstr[0] "%d nap"
msgstr[1] "%d nap"

#: src/humanize/time.py:197
msgid "a month"
msgstr "egy hónap"

#: src/humanize/time.py:199
#, python-format
msgid "%d month"
msgid_plural "%d months"
msgstr[0] "%d hónap"
msgstr[1] "%d hónap"

#: src/humanize/time.py:203
msgid "a year"
msgstr "egy év"

#: src/humanize/time.py:206 src/humanize/time.py:217
#, python-format
msgid "1 year, %d day"
msgid_plural "1 year, %d days"
msgstr[0] "egy év és %d nap"
msgstr[1] "egy év és %d nap"

#: src/humanize/time.py:210
msgid "1 year, 1 month"
msgstr "egy év és egy hónap"

#: src/humanize/time.py:213
#, python-format
msgid "1 year, %d month"
msgid_plural "1 year, %d months"
msgstr[0] "egy év és %d hónap"
msgstr[1] "egy év és %d hónap"

#: src/humanize/time.py:219
#, python-format
msgid "%d year"
msgid_plural "%d years"
msgstr[0] "%d év"
msgstr[1] "%d év"

#: src/humanize/time.py:256
#, python-format
msgid "%s from now"
msgstr "%s múlva"

#: src/humanize/time.py:256
#, python-format
msgid "%s ago"
msgstr "azóta eltelt %s"

#: src/humanize/time.py:260
msgid "now"
msgstr "most"

#: src/humanize/time.py:284
msgid "today"
msgstr "ma"

#: src/humanize/time.py:287
msgid "tomorrow"
msgstr "holnap"

#: src/humanize/time.py:290
msgid "yesterday"
msgstr "tegnap"

#: src/humanize/time.py:600
#, python-format
msgid "%s and %s"
msgstr "%s és %s"
