# German translation for humanize.
# Copyright (C) 2016 THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the humanize package.
# <PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: humanize\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-08 19:22+0200\n"
"PO-Revision-Date: 2016-12-18 11:50+0100\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: German\n"
"Language: de\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: <PERSON>\n"
"X-Generator: Sublime Text 3\n"

#: src/humanize/number.py:84
msgctxt "0 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:85
msgctxt "1 (male)"
msgid "st"
msgstr "."

#: src/humanize/number.py:86
msgctxt "2 (male)"
msgid "nd"
msgstr "."

#: src/humanize/number.py:87
msgctxt "3 (male)"
msgid "rd"
msgstr "."

#: src/humanize/number.py:88
msgctxt "4 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:89
msgctxt "5 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:90
msgctxt "6 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:91
msgctxt "7 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:92
msgctxt "8 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:93
msgctxt "9 (male)"
msgid "th"
msgstr "."

#: src/humanize/number.py:97
msgctxt "0 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:98
msgctxt "1 (female)"
msgid "st"
msgstr "."

#: src/humanize/number.py:99
msgctxt "2 (female)"
msgid "nd"
msgstr "."

#: src/humanize/number.py:100
msgctxt "3 (female)"
msgid "rd"
msgstr "."

#: src/humanize/number.py:101
msgctxt "4 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:102
msgctxt "5 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:103
msgctxt "6 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:104
msgctxt "7 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:105
msgctxt "8 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:106
msgctxt "9 (female)"
msgid "th"
msgstr "."

#: src/humanize/number.py:178
msgid "thousand"
msgid_plural "thousand"
msgstr[0] "Tausend"
msgstr[1] "Tausend"

#: src/humanize/number.py:179
msgid "million"
msgid_plural "million"
msgstr[0] "Million"
msgstr[1] "Millionen"

#: src/humanize/number.py:180
msgid "billion"
msgid_plural "billion"
msgstr[0] "Milliarde"
msgstr[1] "Milliarden"

#: src/humanize/number.py:181
msgid "trillion"
msgid_plural "trillion"
msgstr[0] "Billion"
msgstr[1] "Billionen"

#: src/humanize/number.py:182
msgid "quadrillion"
msgid_plural "quadrillion"
msgstr[0] "Billiarde"
msgstr[1] "Billiarden"

#: src/humanize/number.py:183
msgid "quintillion"
msgid_plural "quintillion"
msgstr[0] "Trillion"
msgstr[1] "Trillionen"

#: src/humanize/number.py:184
msgid "sextillion"
msgid_plural "sextillion"
msgstr[0] "Trilliarde"
msgstr[1] "Trilliarden"

#: src/humanize/number.py:185
msgid "septillion"
msgid_plural "septillion"
msgstr[0] "Quadrillion"
msgstr[1] "Quadrillionen"

#: src/humanize/number.py:186
msgid "octillion"
msgid_plural "octillion"
msgstr[0] "Quadrillarde"
msgstr[1] "Quadrillarden"

#: src/humanize/number.py:187
msgid "nonillion"
msgid_plural "nonillion"
msgstr[0] "Quintillion"
msgstr[1] "Quintillionen"

#: src/humanize/number.py:188
msgid "decillion"
msgid_plural "decillion"
msgstr[0] "Quintilliarde"
msgstr[1] "Quintilliarden"

#: src/humanize/number.py:189
msgid "googol"
msgid_plural "googol"
msgstr[0] "Googol"
msgstr[1] "Googol"

#: src/humanize/number.py:301
msgid "zero"
msgstr "null"

#: src/humanize/number.py:302
msgid "one"
msgstr "eins"

#: src/humanize/number.py:303
msgid "two"
msgstr "zwei"

#: src/humanize/number.py:304
msgid "three"
msgstr "drei"

#: src/humanize/number.py:305
msgid "four"
msgstr "vier"

#: src/humanize/number.py:306
msgid "five"
msgstr "fünf"

#: src/humanize/number.py:307
msgid "six"
msgstr "sechs"

#: src/humanize/number.py:308
msgid "seven"
msgstr "sieben"

#: src/humanize/number.py:309
msgid "eight"
msgstr "acht"

#: src/humanize/number.py:310
msgid "nine"
msgstr "neun"

#: src/humanize/time.py:152
#, fuzzy, python-format
msgid "%d microsecond"
msgid_plural "%d microseconds"
msgstr[0] "%d Mikrosekunde"
msgstr[1] "%d Mikrosekunden"

#: src/humanize/time.py:161
#, fuzzy, python-format
msgid "%d millisecond"
msgid_plural "%d milliseconds"
msgstr[0] "%d Millisekunde"
msgstr[1] "%d Millisekunden"

#: src/humanize/time.py:164 src/humanize/time.py:259
msgid "a moment"
msgstr "ein Moment"

#: src/humanize/time.py:167
msgid "a second"
msgstr "eine Sekunde"

#: src/humanize/time.py:170
#, python-format
msgid "%d second"
msgid_plural "%d seconds"
msgstr[0] "%d Sekunde"
msgstr[1] "%d Sekunden"

#: src/humanize/time.py:173
msgid "a minute"
msgstr "eine Minute"

#: src/humanize/time.py:177
#, python-format
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] "%d Minute"
msgstr[1] "%d Minuten"

#: src/humanize/time.py:180
msgid "an hour"
msgstr "eine Stunde"

#: src/humanize/time.py:184
#, python-format
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] "%d Stunde"
msgstr[1] "%d Stunden"

#: src/humanize/time.py:188
msgid "a day"
msgstr "ein Tag"

#: src/humanize/time.py:191 src/humanize/time.py:194
#, python-format
msgid "%d day"
msgid_plural "%d days"
msgstr[0] "%d Tag"
msgstr[1] "%d Tage"

#: src/humanize/time.py:197
msgid "a month"
msgstr "ein Monat"

#: src/humanize/time.py:199
#, python-format
msgid "%d month"
msgid_plural "%d months"
msgstr[0] "%d Monat"
msgstr[1] "%d Monate"

#: src/humanize/time.py:203
msgid "a year"
msgstr "ein Jahr"

#: src/humanize/time.py:206 src/humanize/time.py:217
#, python-format
msgid "1 year, %d day"
msgid_plural "1 year, %d days"
msgstr[0] "ein Jahr und %d Tag"
msgstr[1] "ein Jahr und %d Tage"

#: src/humanize/time.py:210
msgid "1 year, 1 month"
msgstr "ein Monat"

#: src/humanize/time.py:213
#, python-format
msgid "1 year, %d month"
msgid_plural "1 year, %d months"
msgstr[0] "ein Jahr und %d Monat"
msgstr[1] "ein Jahr und %d Monate"

#: src/humanize/time.py:219
#, python-format
msgid "%d year"
msgid_plural "%d years"
msgstr[0] "%d Jahr"
msgstr[1] "%d Jahre"

#: src/humanize/time.py:256
#, python-format
msgid "%s from now"
msgstr "%s ab jetzt"

#: src/humanize/time.py:256
#, python-format
msgid "%s ago"
msgstr "vor %s"

#: src/humanize/time.py:260
msgid "now"
msgstr "jetzt"

#: src/humanize/time.py:284
msgid "today"
msgstr "heute"

#: src/humanize/time.py:287
msgid "tomorrow"
msgstr "morgen"

#: src/humanize/time.py:290
msgid "yesterday"
msgstr "gestern"

#: src/humanize/time.py:600
#, python-format
msgid "%s and %s"
msgstr "%s und %s"
